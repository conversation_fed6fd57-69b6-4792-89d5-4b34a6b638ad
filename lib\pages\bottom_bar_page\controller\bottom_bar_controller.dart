import 'dart:async';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import 'package:shortie/pages/feed_page/view/feed_view.dart';
import 'package:shortie/pages/message_page/view/message_view.dart';
import 'package:shortie/pages/profile_page/view/profile_view.dart';
import 'package:shortie/pages/reels_page/view/reels_view.dart';
import 'package:shortie/pages/stream_page/view/stream_view.dart';
import 'package:shortie/routes/app_routes.dart';
import 'package:shortie/ui/send_gift_on_video_bottom_sheet_ui.dart';
import 'package:shortie/utils/branch_io_services.dart';
import 'package:shortie/utils/socket_services.dart';
import 'package:shortie/utils/utils.dart';

class BottomBarController extends GetxController {
  int selectedTabIndex = 0;
  PageController pageController = PageController();

  // Flag to prevent multiple simultaneous navigation attempts
  bool _isNavigating = false;

  @override
  void onInit() {
    init();
    super.onInit();
  }

  void init() async {
    selectedTabIndex = 0;

    // Initialize socket connection
    await SocketServices.socketConnect();

    // Initialize gift system
    SendGiftOnVideoBottomSheetUi.onGetGift();

    // Handle Branch.io deep links with proper timing
    await _handleBranchIoEvents();
  }

  /// Handle different types of Branch.io deep link events
  Future<void> _handleBranchIoEvents() async {
    // Prevent multiple simultaneous navigation attempts
    if (_isNavigating) {
      Utils.showLog("Navigation already in progress, skipping Branch.io event handling");
      return;
    }

    _isNavigating = true;

    try {
      if (BranchIoServices.eventType == "Post") {
        Utils.showLog("Handling Branch.io Post event");
        await 300.milliseconds.delay();
        onChangeBottomBar(2);

      } else if (BranchIoServices.eventType == "Profile") {
        Utils.showLog("Handling Branch.io Profile event");
        await 300.milliseconds.delay();
        onChangeBottomBar(2);
        if (BranchIoServices.eventId.isNotEmpty) {
          await 200.milliseconds.delay(); // Small delay before secondary navigation
          Get.toNamed(AppRoutes.previewUserProfilePage, arguments: BranchIoServices.eventId);
        }

      } else if (BranchIoServices.eventType == "Video") {
        Utils.showLog("Handling Branch.io Video event - Video ID: ${BranchIoServices.eventId}");
        await _handleVideoDeepLink();

      } else {
        Utils.showLog("No Branch.io event detected, proceeding with normal flow");
      }
    } catch (e) {
      Utils.showLog("Error handling Branch.io events: $e");
    } finally {
      // Clear the event data after handling to prevent re-processing
      _clearBranchIoEventData();

      // Reset navigation flag after a delay
      Timer(const Duration(milliseconds: 1000), () {
        _isNavigating = false;
      });
    }
  }

  /// Handle video deep link navigation
  Future<void> _handleVideoDeepLink() async {
    if (BranchIoServices.eventId.isEmpty) {
      Utils.showLog("Video ID is empty, falling back to normal reels flow");
      // Use shorter delay to prevent navigation conflicts
      await 300.milliseconds.delay();
      onChangeBottomBar(0); // Navigate to reels page
      return;
    }

    try {
      Utils.showLog("Attempting to navigate to specific video: ${BranchIoServices.eventId}");

      // Navigate to reels page with optimized timing
      await 300.milliseconds.delay();
      onChangeBottomBar(0);

      // Reduced wait time to prevent GlobalKey conflicts
      await 500.milliseconds.delay();

      // The reels controller will automatically load the specific video
      Utils.showLog("Reels page should now load the specific video");

    } catch (e) {
      Utils.showLog("Error navigating to specific video: $e");
      // Fallback to normal reels flow with minimal delay
      await 300.milliseconds.delay();
      onChangeBottomBar(0);
    }
  }

  /// Clear Branch.io event data to prevent re-processing
  void _clearBranchIoEventData() {
    final eventType = BranchIoServices.eventType;
    final eventId = BranchIoServices.eventId;

    BranchIoServices.eventType = "";
    BranchIoServices.eventId = "";

    Utils.showLog("Cleared Branch.io event data - Type: $eventType, ID: $eventId");
  }

  List bottomBarPages = [
    const ReelsView(),
    const StreamView(),
    const FeedView(),
    const MessageView(),
    const ProfileView(),
  ];

  void onChangeBottomBar(int index) {
    if (index != selectedTabIndex) {
      selectedTabIndex = index;
      update(["onChangeBottomBar"]);
    }
  }
}
