import 'package:flutter/material.dart';
import 'package:get/get.dart';

import 'package:shortie/pages/feed_page/view/feed_view.dart';
import 'package:shortie/pages/message_page/view/message_view.dart';
import 'package:shortie/pages/profile_page/view/profile_view.dart';
import 'package:shortie/pages/reels_page/view/reels_view.dart';
import 'package:shortie/pages/stream_page/view/stream_view.dart';
import 'package:shortie/routes/app_routes.dart';
import 'package:shortie/ui/send_gift_on_video_bottom_sheet_ui.dart';
import 'package:shortie/utils/branch_io_services.dart';
import 'package:shortie/utils/socket_services.dart';
import 'package:shortie/utils/utils.dart';

class BottomBarController extends GetxController {
  int selectedTabIndex = 0;
  PageController pageController = PageController();

  @override
  void onInit() {
    init();
    super.onInit();
  }

  void init() async {
    selectedTabIndex = 0;

    await SocketServices.socketConnect();

    SendGiftOnVideoBottomSheetUi.onGetGift();

    // Handle Branch.io deep links
    await _handleBranchIoEvents();
  }

  /// Handle different types of Branch.io deep link events
  Future<void> _handleBranchIoEvents() async {
    try {
      if (BranchIoServices.eventType == "Post") {
        Utils.showLog("Handling Branch.io Post event");
        await 500.milliseconds.delay();
        onChangeBottomBar(2);

      } else if (BranchIoServices.eventType == "Profile") {
        Utils.showLog("Handling Branch.io Profile event");
        await 500.milliseconds.delay();
        onChangeBottomBar(2);
        if (BranchIoServices.eventId.isNotEmpty) {
          Get.toNamed(AppRoutes.previewUserProfilePage, arguments: BranchIoServices.eventId);
        }

      } else if (BranchIoServices.eventType == "Video") {
        Utils.showLog("Handling Branch.io Video event - Video ID: ${BranchIoServices.eventId}");
        await _handleVideoDeepLink();

      } else {
        Utils.showLog("No Branch.io event detected, proceeding with normal flow");
      }
    } catch (e) {
      Utils.showLog("Error handling Branch.io events: $e");
    } finally {
      // Clear the event data after handling to prevent re-processing
      _clearBranchIoEventData();
    }
  }

  /// Handle video deep link navigation
  Future<void> _handleVideoDeepLink() async {
    if (BranchIoServices.eventId.isEmpty) {
      Utils.showLog("Video ID is empty, falling back to normal reels flow");
      await 500.milliseconds.delay();
      onChangeBottomBar(0); // Navigate to reels page
      return;
    }

    try {
      Utils.showLog("Attempting to navigate to specific video: ${BranchIoServices.eventId}");

      // Navigate to reels page first
      await 500.milliseconds.delay();
      onChangeBottomBar(0);

      // Wait a bit more for the reels page to initialize
      await 1000.milliseconds.delay();

      // The reels controller will automatically load the specific video
      // because it uses BranchIoServices.eventId in the API call
      Utils.showLog("Reels page should now load the specific video");

    } catch (e) {
      Utils.showLog("Error navigating to specific video: $e");
      // Fallback to normal reels flow
      await 500.milliseconds.delay();
      onChangeBottomBar(0);
    }
  }

  /// Clear Branch.io event data to prevent re-processing
  void _clearBranchIoEventData() {
    final eventType = BranchIoServices.eventType;
    final eventId = BranchIoServices.eventId;

    BranchIoServices.eventType = "";
    BranchIoServices.eventId = "";

    Utils.showLog("Cleared Branch.io event data - Type: $eventType, ID: $eventId");
  }

  List bottomBarPages = [
    const ReelsView(),
    const StreamView(),
    const FeedView(),
    const MessageView(),
    const ProfileView(),
  ];

  void onChangeBottomBar(int index) {
    if (index != selectedTabIndex) {
      selectedTabIndex = index;
      update(["onChangeBottomBar"]);
    }
  }
}
