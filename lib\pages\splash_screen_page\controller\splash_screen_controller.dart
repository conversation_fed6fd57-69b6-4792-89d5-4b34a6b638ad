import 'dart:async';

import 'package:get/get.dart';
import 'package:shortie/components/beauty_effects/zego_beauty_effect_sheet.dart';
import 'package:shortie/pages/splash_screen_page/api/admin_setting_api.dart';
import 'package:shortie/routes/app_routes.dart';
import 'package:shortie/utils/branch_io_services.dart';
import 'package:shortie/utils/database.dart';
import 'package:shortie/utils/enums.dart';
import 'package:shortie/utils/internet_connection.dart';
import 'package:shortie/utils/request.dart';
import 'package:shortie/utils/utils.dart';
import 'package:shortie/zego_sdk_manager.dart';

class SplashScreenController extends GetxController {
  @override
  void onInit() {
    init();
    super.onInit();
  }

  Future<void> init() async {
    await AppRequest.notificationPermission();

    if (InternetConnection.isConnect.value) {
      await AdminSettingsApi.callApi(); // Get Admin Setting Data...
      if (AdminSettingsApi.adminSettingModel?.data != null) {
        // Initialize Zego Express Engine
        await Utils.onInitCreateEngine();
        
        // Initialize ZEGO SDK Manager with effects
        await ZEGOSDKManager().init(initializeEffects: true);

        // Reset beauty effects global state to prevent conflicts
        resetBeautyEffectGlobalState();

        // Initialize Payment
        await Utils.onInitPayment();

        await splashScreen();
      } else {
        Utils.showToast(EnumLocal.txtSomeThingWentWrong.name.tr);
        Utils.showLog("Admin Setting Api Calling Failed !!");
      }
    } else {
      Utils.showToast(EnumLocal.txtConnectionLost.name.tr);
      Utils.showLog("Internet Connection Lost !!");
    }
  }

  Future<void> splashScreen() async {
    Timer(
      Duration(milliseconds: 100),
      () async {
        // Check User Is Login Or Not...
        if (Database.isNewUser == false && Database.fetchLoginUserProfileModel?.user?.id != null) {
          BranchIoServices.onListenBranchIoLinks();

          // Check for Branch.io deep link video before navigating
          await _handleBranchIoVideoLink();

          Get.offAllNamed(AppRoutes.bottomBarPage);
        } else {
          Get.offAllNamed(AppRoutes.onBoardingPage);
        }
      },
    );
  }

  /// Handle Branch.io video deep links during app initialization
  Future<void> _handleBranchIoVideoLink() async {
    try {
      // Check if app was opened via Branch.io video link
      if (BranchIoServices.eventType == "Video" && BranchIoServices.eventId.isNotEmpty) {
        Utils.showLog("Branch.io Video Link Detected - Video ID: ${BranchIoServices.eventId}");

        // Set a flag to indicate we have a specific video to show
        // This will be handled by the bottom bar controller
        Utils.showLog("Video deep link will be processed by bottom bar controller");
      } else {
        Utils.showLog("No Branch.io video link detected, proceeding with normal flow");
      }
    } catch (e) {
      Utils.showLog("Error handling Branch.io video link: $e");
      // Continue with normal flow on error
    }
  }
}
