import 'package:get/get.dart';
import 'package:preload_page_view/preload_page_view.dart';

import 'package:shortie/pages/reels_page/api/fetch_reels_api.dart';
import 'package:shortie/pages/reels_page/model/fetch_reels_model.dart';
import 'package:shortie/utils/branch_io_services.dart';
import 'package:shortie/utils/database.dart';
import 'package:shortie/utils/utils.dart';

class ReelsController extends GetxController {
  PreloadPageController preloadPageController = PreloadPageController();

  bool isLoadingReels = false;
  FetchReelsModel? fetchReelsModel;

  bool isPaginationLoading = false;

  List<Data> mainReels = [];

  int currentPageIndex = 0;

  // Track if we're loading a specific video from Branch.io
  bool isLoadingSpecificVideo = false;
  String? targetVideoId;

  Future<void> init() async {
    currentPageIndex = 0;
    mainReels.clear();
    FetchReelsApi.startPagination = 0;
    isLoadingReels = true;

    // Check if we have a specific video to load from Branch.io
    _checkForSpecificVideo();

    update(["onGetReels"]);
    await onGetReels();
    isLoadingReels = false;

    // If we loaded a specific video, try to navigate to it
    if (isLoadingSpecificVideo && targetVideoId != null) {
      _navigateToSpecificVideo();
    }
  }

  /// Check if we need to load a specific video from Branch.io deep link
  void _checkForSpecificVideo() {
    if (BranchIoServices.eventId.isNotEmpty) {
      isLoadingSpecificVideo = true;
      targetVideoId = BranchIoServices.eventId;
      Utils.showLog("Reels Controller: Loading specific video - ID: $targetVideoId");
    } else {
      isLoadingSpecificVideo = false;
      targetVideoId = null;
      Utils.showLog("Reels Controller: Loading normal video feed");
    }
  }

  /// Navigate to the specific video if it was loaded
  void _navigateToSpecificVideo() {
    if (targetVideoId == null || mainReels.isEmpty) {
      Utils.showLog("Cannot navigate to specific video - targetVideoId: $targetVideoId, mainReels.length: ${mainReels.length}");
      return;
    }

    try {
      // Find the index of the target video
      int targetIndex = mainReels.indexWhere((video) => video.id == targetVideoId);

      if (targetIndex != -1) {
        Utils.showLog("Found target video at index: $targetIndex");
        // Navigate to the specific video
        currentPageIndex = targetIndex;
        preloadPageController.animateToPage(
          targetIndex,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );
        update(["onChangePage"]);
        Utils.showLog("Successfully navigated to specific video");
      } else {
        Utils.showLog("Target video not found in loaded videos, showing first video");
        // Video not found, but we still have videos to show
        currentPageIndex = 0;
        update(["onChangePage"]);
      }
    } catch (e) {
      Utils.showLog("Error navigating to specific video: $e");
      // Fallback to first video
      currentPageIndex = 0;
      update(["onChangePage"]);
    } finally {
      // Reset the specific video loading state
      isLoadingSpecificVideo = false;
      targetVideoId = null;

      // Clear Branch.io event data to prevent re-processing
      if (BranchIoServices.eventId.isNotEmpty) {
        Utils.showLog("Clearing Branch.io event data after processing");
        BranchIoServices.eventType = "";
        BranchIoServices.eventId = "";
      }
    }
  }

  void onPagination(int value) async {
    if ((mainReels.length - 1) == value) {
      if (isPaginationLoading == false) {
        isPaginationLoading = true;
        update(["onPagination"]);
        await onGetReels();
        isPaginationLoading = false;
        update(["onPagination"]);
      }
    }
  }

  void onChangePage(int index) async {
    currentPageIndex = index;
    update(["onChangePage"]);
  }

  Future<void> onGetReels() async {
    try {
      fetchReelsModel = null;

      // Use the target video ID if we're loading a specific video, otherwise use empty string
      String videoIdToLoad = isLoadingSpecificVideo ? (targetVideoId ?? "") : "";

      Utils.showLog("Fetching reels with videoId: '$videoIdToLoad'");
      fetchReelsModel = await FetchReelsApi.callApi(
        loginUserId: Database.loginUserId,
        videoId: videoIdToLoad
      );

      if (fetchReelsModel?.data != null) {
        if (fetchReelsModel!.data!.isNotEmpty) {
          final paginationData = fetchReelsModel?.data ?? [];

          Utils.showLog("Loaded ${paginationData.length} videos");
          mainReels.addAll(paginationData);

          update(["onGetReels"]);
        } else {
          Utils.showLog("No videos returned from API");
        }
      } else {
        Utils.showLog("API returned null data");
      }

      if (mainReels.isEmpty) {
        Utils.showLog("No videos available");
        update(["onGetReels"]);
      }
    } catch (e) {
      Utils.showLog("Error fetching reels: $e");

      // If we were trying to load a specific video and it failed, try loading normal feed
      if (isLoadingSpecificVideo) {
        Utils.showLog("Specific video loading failed, falling back to normal feed");
        await _fallbackToNormalFeed();
      }

      if (mainReels.isEmpty) {
        update(["onGetReels"]);
      }
    }
  }

  /// Fallback to normal video feed if specific video loading fails
  Future<void> _fallbackToNormalFeed() async {
    try {
      isLoadingSpecificVideo = false;
      targetVideoId = null;

      Utils.showLog("Loading normal video feed as fallback");
      fetchReelsModel = await FetchReelsApi.callApi(
        loginUserId: Database.loginUserId,
        videoId: ""
      );

      if (fetchReelsModel?.data != null && fetchReelsModel!.data!.isNotEmpty) {
        final paginationData = fetchReelsModel?.data ?? [];
        Utils.showLog("Fallback: Loaded ${paginationData.length} videos");
        mainReels.addAll(paginationData);
        update(["onGetReels"]);
      }
    } catch (e) {
      Utils.showLog("Fallback to normal feed also failed: $e");
    }
  }
}
